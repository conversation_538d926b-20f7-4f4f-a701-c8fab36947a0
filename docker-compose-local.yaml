networks:
  frontend_dev:
    ipam:
      config:
        - subnet: **********/24

services:
  pr-nginx:
    image: nginx:1.27.2
    volumes:
    - ./config/nginx/router-local:/etc/nginx/conf.d/
    ports:
    - 0.0.0.0:80:80
    - 0.0.0.0:443:443
    container_name: pr-nginx
    networks:
      frontend_dev:
        ipv4_address: **********
    depends_on:
    - pr-admin-mysql
    - pr-crons
    - pr-api
    - pr-main
    links:
    - pr-admin-mysql
    - pr-crons
    - pr-api
    - pr-main
    logging:
      driver: "json-file"
      options:
        max-size: "1k"
        max-file: "3"
  pr-db-mysql:
    image: mysql:9.1.0
    restart: always
    networks:
      frontend_dev:
        ipv4_address: **********
    volumes:
      - partymysqldb:/var/lib/mysql
    container_name: pr-db-mysql
    environment:
      MYSQL_PASSWORD: 12345678
      MYSQL_USER: remote
      MYSQL_ROOT_PASSWORD: qwerty
    ports:
      - 3307:3306
    cap_add:
      - SYS_NICE
    logging:
      driver: "json-file"
      options:
        max-size: "1k"
        max-file: "3"
  pr-admin-mysql:
    image: phpmyadmin/phpmyadmin
    container_name: pr-admin-mysql
    restart: unless-stopped
    networks:
      frontend_dev:
        ipv4_address: **********
    environment:
      - PMA_HOST=pr-db-mysql
      - PMA_PORT=3306
      - UPLOAD_LIMIT=1000M
  pr-api:
    build:
      context: ./packages/api
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    networks:
      frontend_dev:
        ipv4_address: **********
    volumes:
      - ./packages/common:/usr/src/common
      - ./packages/api/src:/usr/src/app/src
    depends_on:
      - pr-db-mysql
      - pr-memcached
    container_name: pr-api
    environment:
      - DATABASE_URL=mysql://root:qwerty@pr-db-mysql:3306/party
      - AUTH0_ISSUER_BASE_URL=https://party-explain-games.eu.auth0.com/
      - AUTH0_AUDIENCE=https://lparty-api.explain.games
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=neGo2ueWhdf87CXnr1q6YWy6XbkSYc2Z2FPYn+gP
      - S3_BUCKET=community.explain.games
      - AWS_REGION=eu-central-1
      - IMAGE_CDN=https://party.cdn.explain.games
      - MAIN_HOST=https://lparty.explain.games
      - MYSQL_HOST=pr-db-mysql
      - MYSQL_USER=root
      - MYSQL_PASS=qwerty
      - MYSQL_DB=party
      - MYSQL_PORT=3306
      - MEMC_HOST=pr-memcached
      - MEMC_PORT=11211
      - NODE_ENV=development
      - IMAGE_LINK_PROFILE_PICTURES=profilePictures
      - IMAGE_LINK_LOGO=community/logo
  pr-crons:
    build: ./packages/crons
    volumes:
      - ./packages/crons/files:/var/www/html
    depends_on:
      - pr-db-mysql
    restart: unless-stopped
    container_name: pr-crons
    expose:
      - 80
    networks:
      frontend_dev:
        ipv4_address: **********
    environment:
      - ADMIN_HOST=https://lparty-admin.explain.games
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=neGo2ueWhdf87CXnr1q6YWy6XbkSYc2Z2FPYn+gP
      - S3_BUCKET=community.explain.games
      - AWS_REGION=eu-central-1
      - MEMC_HOST=pr-memcached
      - MEMC_PORT=11211
      - MYSQL_HOST=pr-db-mysql
      - MYSQL_DB=party
      - MYSQL_PORT=3306
      - MYSQL_USER=remote
      - MYSQL_PASS=12345678
      - S3_DEVPATH=dev/
      - PMA_HOST=http://lparty-pma.explain.games/
      - ISDEV=1
  pr-memcached:
    image: memcached:1.6
    networks:
      frontend_dev:
        ipv4_address: **********
    container_name: pr-memcached
    ports:
      - 8888:80
  pr-main:
    build: ./packages/server
    volumes:
      - ./packages/common:/usr/src/common
      - ./packages/server/src:/usr/src/app/src
      - ./packages/api:/usr/src/api
    networks:
      frontend_dev:
        ipv4_address: **********
    depends_on:
      - pr-api
    container_name: pr-main
    restart: unless-stopped
    environment:
      - API_SERVER_URL=https://lparty-api.explain.games
      - API_SERVER=http://pr-api:8888
      - CDN_SERVER_URL=https://party.cdn.explain.games
      - NODE_ENV=dev
      - COMMUNITY_DEFAULT_LOGO=community/logo/default_community_logo.png
      - AUTH0_DOMAIN=party-explain-games.eu.auth0.com
      - AUTH0_CLIENT_ID=SFmeAuULCksIlwzpG9n2vNBnRDf8cyzj
      - AUTH0_REDIRECT_URI=https://lparty.explain.games/loggedin
      - AUTH0_AUDIENCE=https://lparty-api.explain.games
      - FIREBASE_API_KEY=your_api_key
      - FIREBASE_AUTH_DOMAIN=your_auth_domain
      - FIREBASE_PROJECT_ID=490749385166
      - FIREBASE_APP_ID=11341755953-ejch596r4kq4ubbgtg5binn036md75np.apps.googleusercontent.com
volumes:
  partymysqldb:
import { useAuth0 } from "@auth0/auth0-react"
import { getAuth } from "firebase/auth"
import { useEffect } from "react"

import { useUserStore } from "../../store/useUserStore"
import { RouterOutput, trpc } from "../../trpc/trpc"
import { auth } from "../../utils/access"

type UserDataLoaderOutput = RouterOutput["loggedInUser"]
export const UserDataLoader = () => {
  // const { isLoading, isAuthenticated, getAccessTokenSilently } = useAuth0()
  const { setUserData, setUserLoaded } = useUserStore()
  const auth = getAuth(app)

  useEffect(() => {
    if (ENV_MODE === "dev") {
      trpc.loggedInUser.query().then((res: UserDataLoaderOutput) => {
        if (res) {
          setUserData(res)
        }
      })
      return
    }
    const getAccessToken = async () => {
      auth.authToken = await getAccessTokenSilently()
      trpc.loggedInUser.query().then((res: UserDataLoaderOutput) => {
        if (res) {
          setUserData(res)
        }
      })
    }

    if (!isLoading && isAuthenticated) {
      getAccessToken()
    }

    if (!isLoading && !isAuthenticated) {
      setUserLoaded()
    }
  }, [
    isLoading,
    isAuthenticated,
    getAccessTokenSilently,
    setUserLoaded,
    setUserData,
  ])

  return null
}
